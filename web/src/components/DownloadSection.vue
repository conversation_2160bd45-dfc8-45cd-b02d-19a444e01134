<template>
  <section id="download" class="py-16 bg-gray-100">
    <div class="container mx-auto px-4 text-center">
      <h2 class="text-4xl font-bold mb-12">{{ $t('download.title') }}</h2>

      <div class="flex flex-col md:flex-row justify-center items-center gap-8">
        <div class="bg-white p-8 rounded-xl shadow-lg max-w-sm w-full">
          <div class="h-20 w-20 mx-auto mb-6 flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-16 h-16 text-green-500">
              <path d="M17.523 15.34a.75.75 0 0 1-.75-.75V9.41a.75.75 0 0 1 1.5 0v5.18a.75.75 0 0 1-.75.75Zm-11.046 0a.75.75 0 0 1-.75-.75V9.41a.75.75 0 0 1 1.5 0v5.18a.75.75 0 0 1-.75.75Z" />
              <path d="M12 2.25c-4.14 0-7.5 3.024-7.5 6.75v8.25a.75.75 0 0 0 .75.75h3.75v3.75a.75.75 0 0 0 1.5 0V18h3v3.75a.75.75 0 0 0 1.5 0V18h3.75a.75.75 0 0 0 .75-.75V9c0-3.726-3.36-6.75-7.5-6.75Zm0 1.5c3.32 0 6 2.34 6 5.25v7.5H6v-7.5c0-2.91 2.68-5.25 6-5.25Z" />
            </svg>
          </div>
          <h3 class="text-2xl font-semibold mb-4">{{ $t('download.android') }}</h3>
          <p class="mb-6">{{ $t('download.androidText') }}</p>
          <a
            href="/download/kokume.apk"
            download="kokume.apk"
            target="_blank"
            rel="noopener"
            class="bg-primary hover:bg-primary/80 text-white font-bold py-3 px-8 rounded-full inline-block transition-colors duration-300"
          >
            {{ $t('download.androidButton') }}
          </a>
        </div>

<!--        <div class="bg-white p-8 rounded-xl shadow-lg max-w-sm w-full">-->
<!--          <div class="h-20 w-20 mx-auto mb-6 flex items-center justify-center">-->
<!--            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-16 h-16 text-gray-800">-->
<!--              <path d="M11.7 2.805a.75.75 0 0 1 .6 0A60.65 60.65 0 0 1 22.83 8.72a.75.75 0 0 1-.231 1.337 49.948 49.948 0 0 0-9.902 3.912l-.003.002c-.114.06-.227.119-.34.18a.75.75 0 0 1-.707 0A50.88 50.88 0 0 0 7.5 12.173v-.224c0-.131.067-.248.172-.311a54.615 54.615 0 0 1 4.653-2.52.75.75 0 0 0-.65-1.352 56.123 56.123 0 0 0-4.78 2.589 1.858 1.858 0 0 0-.859 1.228 49.803 49.803 0 0 0-4.634-1.527.75.75 0 0 1-.231-1.337A60.653 60.653 0 0 1 11.7 2.805Z" />-->
<!--              <path d="M13.06 15.473a48.45 48.45 0 0 1 7.666-3.282c.134 1.414.22 2.843.255 4.284a.75.75 0 0 1-.46.71 47.87 47.87 0 0 0-8.105 4.342.75.75 0 0 1-.832 0 47.87 47.87 0 0 0-8.104-4.342.75.75 0 0 1-.461-.71c.035-1.442.121-2.87.255-4.286.921.304 1.83.634 2.726.99v1.27a1.5 1.5 0 0 0-.14 2.508c-.09.38-.222.753-.397 1.11.452.213.901.434 1.346.66a6.727 6.727 0 0 0 .551-1.607 1.5 1.5 0 0 0 .14-2.67v-.645a48.549 48.549 0 0 1 3.44 1.667 2.25 2.25 0 0 0 2.12 0Z" />-->
<!--              <path d="M4.462 19.462c.42-.419.753-.89 1-1.395.453.214.902.435 1.347.662a6.742 6.742 0 0 1-1.286 1.794.75.75 0 0 1-1.06-1.06Z" />-->
<!--            </svg>-->
<!--          </div>-->
<!--          <h3 class="text-2xl font-semibold mb-4">{{ $t('download.ios') }}</h3>-->
<!--          <p class="mb-6">{{ $t('download.iosText') }}</p>-->
<!--          <a-->
<!--            href="https://apps.apple.com/app/kokume/id123456789"-->
<!--            target="_blank"-->
<!--            class="bg-primary hover:bg-primary/80 text-white font-bold py-3 px-8 rounded-full inline-block transition-colors duration-300"-->
<!--          >-->
<!--            {{ $t('download.iosButton') }}-->
<!--          </a>-->
<!--        </div>-->
      </div>

      <div class="mt-12">
        <p class="text-lg mb-4">{{ $t('download.comingSoon') }}</p>
        <p class="text-sm text-gray-600">
          {{ $t('download.requirements') }}
        </p>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
// Download section component
</script>
